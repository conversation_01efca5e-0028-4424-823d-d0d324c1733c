#!/usr/bin/env python3
"""
步骤优化聚合器
负责处理测试用例步骤优化的业务逻辑
"""

from typing import Tuple, Optional

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.step_optimize_prompt import build_optimization_prompt
from src.infra.model import get_chat_model


class StepOptimizeAggregate:
    """步骤优化聚合器 - 处理步骤优化的核心业务逻辑"""

    def __init__(self):

        self.model = get_chat_model()

    def optimize_test_steps(self, steps: str) -> Tuple[Optional[str], Optional[str]]:
        """
        使用AI优化测试用例步骤格式

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepOptimizeAggregate: Starting step optimization for: {steps[:100]}...")

            # 构建优化提示
            system_instruction = build_optimization_prompt(steps)

            # 直接调用模型，避免使用ChatPromptTemplate的模板变量解析
            from langchain_core.messages import SystemMessage

            messages = [SystemMessage(content=system_instruction)]
            output = self.model.invoke(messages)
            ai_result = output.content

            logger.info(f"✅ StepOptimizeAggregate: Step optimization completed successfully: {ai_result}")
            return ai_result, None
        except Exception as e:
            logger.error(f"❌ StepOptimizeAggregate: Failed to optimize steps with AI: {str(e)}")
            return None, str(e)


# 创建全局实例
step_optimize_aggregate = StepOptimizeAggregate()
